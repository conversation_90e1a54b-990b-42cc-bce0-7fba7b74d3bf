'use client';

import {
	<PERSON><PERSON>,
	<PERSON>,
	Card<PERSON>ontent,
	<PERSON><PERSON><PERSON>er,
	<PERSON><PERSON><PERSON>le,
	Button,
	ScrollArea,
} from '@/components/ui';
import { useDOMFloating } from '@/hooks/use-dom-floating';
import { BookOpen, X } from 'lucide-react';
import { useState } from 'react';
import { ERROR_CATEGORIES } from './constants';
import { ErrorCategory } from './types';

interface ErrorTypesGuidanceProps {
	id?: string;
	className?: string;
	defaultOpen?: boolean;
}

export function ErrorTypesGuidance({
	id = 'error-types-guidance',
	className = '',
	defaultOpen = false,
}: ErrorTypesGuidanceProps) {
	const [isOpen, setIsOpen] = useState(defaultOpen);

	// Guidance content component
	const guidanceContent = (
		<div className="floating-guidance-panel">
			{!isOpen ? (
				// Guidance button
				<Button
					size="icon"
					className="h-14 w-14 rounded-full shadow-lg bg-blue-600 hover:bg-blue-700 text-white hover:shadow-xl transition-all duration-200 hover:scale-105"
					onClick={() => setIsOpen(true)}
					title="Xem thông tin các loại lỗi tiếng Anh"
					aria-label="Xem thông tin các loại lỗi tiếng Anh"
				>
					<BookOpen className="h-6 w-6" />
				</Button>
			) : (
				// Guidance panel
				<Card className="border bg-background shadow-lg max-w-4xl max-h-[80vh] overflow-hidden">
					<CardHeader className="pb-4">
						<div className="flex items-center justify-between">
							<CardTitle className="flex items-center gap-3">
								<div className="p-2 rounded-lg bg-blue-600 text-white">
									<BookOpen className="h-5 w-5" />
								</div>
								<div>
									<div className="text-xl font-bold">
										📚 Phân loại các loại lỗi tiếng Anh
									</div>
									<div className="text-sm text-muted-foreground font-normal">
										Từ dễ nhận biết nhất đến khó nhận biết nhất
									</div>
								</div>
							</CardTitle>
							<Button
								size="icon"
								variant="ghost"
								className="h-8 w-8"
								onClick={() => setIsOpen(false)}
							>
								<X className="h-4 w-4" />
							</Button>
						</div>
					</CardHeader>
					<CardContent>
						<ScrollArea className="h-[60vh] pr-4">
							<div className="space-y-6">
								{ERROR_CATEGORIES.map((category) => (
									<ErrorCategorySection key={category.id} category={category} />
								))}

								<div className="mt-8 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
									<h3 className="font-semibold text-blue-800 dark:text-blue-200 mb-2">
										💡 Gợi ý:
									</h3>
									<ul className="text-sm text-blue-700 dark:text-blue-300 space-y-1">
										<li>
											• <strong>Người mới học</strong> nên tập trung sửa các
											lỗi ở nhóm 🟢 và 🟡
										</li>
										<li>
											• <strong>Người học nâng cao</strong> nên chú ý nhóm 🟠
											và 🔴 để tăng độ tự nhiên, chính xác
										</li>
										<li>
											• Trong grammar practice, độ khó sẽ quyết định loại lỗi
											nào được tạo ra
										</li>
									</ul>
								</div>
							</div>
						</ScrollArea>
					</CardContent>
				</Card>
			)}
		</div>
	);

	// Use DOM floating UI system
	useDOMFloating(id, guidanceContent, {
		position: { bottom: 80, right: 16 },
		zIndex: 1100,
		autoShow: defaultOpen,
		className,
	});

	return null; // Content is rendered through DOM floating UI system
}

function ErrorCategorySection({ category }: { category: ErrorCategory }) {
	return (
		<div className="border border-border rounded-lg p-4 space-y-4">
			<div className="flex items-center gap-3">
				<span className="text-2xl">{category.icon}</span>
				<div className="flex-1">
					<h3 className="text-lg font-semibold">{category.name}</h3>
					<p className="text-sm text-muted-foreground">{category.description}</p>
				</div>
				<Badge className={category.color}>{category.name}</Badge>
			</div>

			<div className="grid gap-3">
				{category.errors.map((error) => (
					<div
						key={error.id}
						className="bg-background/50 border border-border rounded-md p-3 space-y-2"
					>
						<div className="flex items-start justify-between gap-2">
							<h4 className="font-medium text-sm">{error.name}</h4>
							<Badge variant="outline" className={getCategoryColor(error.category)}>
								{getCategoryLabel(error.category)}
							</Badge>
						</div>
						<p className="text-xs text-muted-foreground">{error.description}</p>
						<div className="bg-muted/50 rounded p-2">
							<code className="text-xs font-mono">{error.example}</code>
						</div>
					</div>
				))}
			</div>
		</div>
	);
}

function getCategoryColor(category: string): string {
	switch (category) {
		case 'grammar':
			return 'border-blue-200 text-blue-700 dark:border-blue-700 dark:text-blue-300';
		case 'vocabulary':
			return 'border-purple-200 text-purple-700 dark:border-purple-700 dark:text-purple-300';
		case 'mechanics':
			return 'border-green-200 text-green-700 dark:border-green-700 dark:text-green-300';
		case 'style':
			return 'border-orange-200 text-orange-700 dark:border-orange-700 dark:text-orange-300';
		default:
			return 'border-gray-200 text-gray-700 dark:border-gray-700 dark:text-gray-300';
	}
}

function getCategoryLabel(category: string): string {
	switch (category) {
		case 'grammar':
			return 'Ngữ pháp';
		case 'vocabulary':
			return 'Từ vựng';
		case 'mechanics':
			return 'Cơ học';
		case 'style':
			return 'Phong cách';
		default:
			return 'Khác';
	}
}
