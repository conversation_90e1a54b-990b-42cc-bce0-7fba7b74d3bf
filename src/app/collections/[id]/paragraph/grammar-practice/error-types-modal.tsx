'use client';

import {
	<PERSON><PERSON>,
	<PERSON>alog,
	DialogContent,
	DialogDescription,
	DialogHeader,
	DialogTitle,
	ScrollArea,
} from '@/components/ui';
import { ERROR_CATEGORIES } from './constants';
import { ErrorCategory } from './types';

interface ErrorTypesModalProps {
	open: boolean;
	onOpenChange: (open: boolean) => void;
}

export function ErrorTypesModal({ open, onOpenChange }: ErrorTypesModalProps) {
	return (
		<Dialog open={open} onOpenChange={onOpenChange}>
			<DialogContent className="max-w-4xl max-h-[80vh] overflow-hidden">
				<DialogHeader>
					<DialogTitle className="text-2xl font-bold text-center">
						📚 Phân loại các loại lỗi tiếng Anh
					</DialogTitle>
					<DialogDescription className="text-center text-base">
						Từ dễ nhận biết nhất đến kh<PERSON> nhận biết nhất
					</DialogDescription>
				</DialogHeader>

				<ScrollArea className="h-[60vh] pr-4">
					<div className="space-y-6">
						{ERROR_CATEGORIES.map((category) => (
							<ErrorCategorySection key={category.id} category={category} />
						))}

						<div className="mt-8 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
							<h3 className="font-semibold text-blue-800 dark:text-blue-200 mb-2">
								💡 Gợi ý:
							</h3>
							<ul className="text-sm text-blue-700 dark:text-blue-300 space-y-1">
								<li>
									• <strong>Người mới học</strong> nên tập trung sửa các lỗi ở
									nhóm 🟢 và 🟡
								</li>
								<li>
									• <strong>Người học nâng cao</strong> nên chú ý nhóm 🟠 và 🔴 để
									tăng độ tự nhiên, chính xác
								</li>
								<li>
									• Trong grammar practice, độ khó sẽ quyết định loại lỗi nào được
									tạo ra
								</li>
							</ul>
						</div>
					</div>
				</ScrollArea>
			</DialogContent>
		</Dialog>
	);
}

function ErrorCategorySection({ category }: { category: ErrorCategory }) {
	return (
		<div className="border border-border rounded-lg p-4 space-y-4">
			<div className="flex items-center gap-3">
				<span className="text-2xl">{category.icon}</span>
				<div className="flex-1">
					<h3 className="text-lg font-semibold">{category.name}</h3>
					<p className="text-sm text-muted-foreground">{category.description}</p>
				</div>
				<Badge className={category.color}>{category.name}</Badge>
			</div>

			<div className="grid gap-3">
				{category.errors.map((error) => (
					<div
						key={error.id}
						className="bg-background/50 border border-border rounded-md p-3 space-y-2"
					>
						<div className="flex items-start justify-between gap-2">
							<h4 className="font-medium text-sm">{error.name}</h4>
							<Badge variant="outline" className={getCategoryColor(error.category)}>
								{getCategoryLabel(error.category)}
							</Badge>
						</div>
						<p className="text-xs text-muted-foreground">{error.description}</p>
						<div className="bg-muted/50 rounded p-2">
							<code className="text-xs font-mono">{error.example}</code>
						</div>
					</div>
				))}
			</div>
		</div>
	);
}

function getCategoryColor(category: string): string {
	switch (category) {
		case 'grammar':
			return 'border-blue-200 text-blue-700 dark:border-blue-700 dark:text-blue-300';
		case 'vocabulary':
			return 'border-purple-200 text-purple-700 dark:border-purple-700 dark:text-purple-300';
		case 'mechanics':
			return 'border-green-200 text-green-700 dark:border-green-700 dark:text-green-300';
		case 'style':
			return 'border-orange-200 text-orange-700 dark:border-orange-700 dark:text-orange-300';
		default:
			return 'border-gray-200 text-gray-700 dark:border-gray-700 dark:text-gray-300';
	}
}

function getCategoryLabel(category: string): string {
	switch (category) {
		case 'grammar':
			return 'Ngữ pháp';
		case 'vocabulary':
			return 'Từ vựng';
		case 'mechanics':
			return 'Cơ học';
		case 'style':
			return 'Phong cách';
		default:
			return 'Khác';
	}
}
