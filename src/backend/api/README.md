# API Directory

This directory contains the API layer handlers that manage HTTP requests, authentication, input validation, and orchestration for all backend endpoints.

## Structure

```
api/
├── index.ts                    # API exports and utilities
├── auth.api.ts                 # Authentication endpoints
├── collection.api.ts           # Collection CRUD operations
├── collection-stats.api.ts     # Collection statistics endpoints
├── feedback.api.ts             # User feedback submission
├── keyword.api.ts              # Keyword management endpoints
├── last-seen-word.api.ts       # Spaced repetition tracking
├── llm.api.ts                  # AI/LLM integration endpoints
├── token-monitor.api.ts        # Token usage monitoring
├── user.api.ts                 # User management endpoints
└── word.api.ts                 # Vocabulary word operations
```

## API Layer Responsibilities

### Request Handling
- **HTTP Request Processing**: Parse and validate incoming HTTP requests
- **Input Validation**: Validate request parameters using Zod schemas
- **Authentication**: Verify user authentication and authorization
- **Response Formatting**: Format and return standardized API responses
- **Error Handling**: Catch and transform errors into user-friendly responses

### Authentication & Authorization
- **JWT Token Verification**: Validate JWT tokens from cookies or headers
- **User Context**: Extract user information from authenticated requests
- **Permission Checks**: Verify user permissions for specific operations
- **Session Management**: Handle user session state and validation

### Service Orchestration
- **Service Integration**: Call appropriate services via dependency injection
- **Business Logic Delegation**: Delegate complex operations to service layer
- **Data Transformation**: Transform service responses for API consumption
- **Cross-Service Coordination**: Coordinate operations across multiple services

## API Endpoints

### Authentication (`auth.api.ts`)
- User login with multiple providers (Telegram, Google, Username/Password)
- User logout and session termination
- Token refresh and validation
- Provider-specific authentication flows

### Collections (`collection.api.ts`)
- Collection CRUD operations (Create, Read, Update, Delete)
- Collection word and term management
- Collection sharing and collaboration features
- Collection import/export functionality

### Collection Statistics (`collection-stats.api.ts`)
- Daily progress tracking and analytics
- Learning statistics and metrics
- Performance insights and reports
- Progress visualization data

### User Management (`user.api.ts`)
- User profile CRUD operations
- User settings and preferences management
- Account management and deletion
- User activity tracking

### Vocabulary (`word.api.ts`)
- Word search and retrieval operations
- Vocabulary review and practice logic
- Word definition and example management
- Spaced repetition algorithm integration

### AI Integration (`llm.api.ts`)
- AI-powered content generation
- Vocabulary and definition creation
- Paragraph and question generation
- Grammar practice exercise creation
- Translation and evaluation services

### Feedback (`feedback.api.ts`)
- User feedback submission and processing
- Bug reports and feature requests
- User experience feedback collection
- Feedback analytics and reporting

### Keywords (`keyword.api.ts`)
- Keyword CRUD operations for authenticated users
- Search and discovery functionality
- Keyword categorization and tagging
- Content indexing and retrieval

### Spaced Repetition (`last-seen-word.api.ts`)
- Last seen word tracking for users
- Review scheduling and optimization
- Learning progress monitoring
- Retention analytics and insights

### Monitoring (`token-monitor.api.ts`)
- API token usage monitoring and analytics
- Rate limiting and throttling management
- Performance metrics and optimization
- Cost tracking and budget management

## Design Principles

### Clean Architecture
- **Single Responsibility**: Each API handler has a focused purpose
- **Dependency Injection**: Services injected via wire.ts container
- **Layer Separation**: No direct database access, only through services
- **Error Boundaries**: Comprehensive error handling and recovery

### Security Implementation
- **Input Validation**: All inputs validated using Zod schemas
- **Authentication**: JWT-based authentication for protected endpoints
- **Authorization**: Role-based access control and permissions
- **Rate Limiting**: Request throttling and abuse prevention

### Performance Optimization
- **Caching**: Response caching for frequently accessed data
- **Pagination**: Efficient data pagination for large datasets
- **Optimization**: Query optimization and efficient data retrieval
- **Monitoring**: Performance monitoring and bottleneck identification

## Usage Patterns

### API Handler Structure
```typescript
'use server';

import { ValidationError, UnauthorizedError } from '@/backend/errors';
import { getServiceName } from '@/backend/wire';
import { auth } from '@/lib';
import { z } from 'zod';

const RequestSchema = z.object({
  // Request validation schema
});

export async function apiHandler(request: Request) {
  // Authentication
  const user = await auth();
  if (!user) throw new UnauthorizedError('Authentication required');

  // Input validation
  const data = RequestSchema.parse(await request.json());

  // Service delegation
  const service = getServiceName();
  const result = await service.performOperation(data);

  // Response formatting
  return Response.json(result);
}
```

### Error Handling
```typescript
try {
  const result = await service.operation();
  return Response.json(result);
} catch (error) {
  if (error instanceof ValidationError) {
    return Response.json({ error: error.message }, { status: 400 });
  }
  throw error; // Let global error handler manage
}
```

## Development Guidelines

### API Implementation
- Use `'use server'` directive at the top of all API files
- Implement comprehensive input validation with Zod schemas
- Use dependency injection for service access via wire.ts
- Follow RESTful conventions for endpoint design
- Include proper error handling and status codes

### Authentication & Security
- Verify authentication for all protected endpoints
- Validate user permissions for specific operations
- Sanitize and validate all input data
- Implement rate limiting for abuse prevention
- Use HTTPS and secure headers in production

### Performance & Monitoring
- Implement response caching where appropriate
- Use pagination for large data sets
- Monitor API performance and usage metrics
- Optimize database queries through service layer
- Track and analyze API usage patterns
